import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import { searchAndDownload } from './services/searchService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.post('/api/search', async (req, res) => {
  try {
    console.log('Received search request');
    console.log('Request body:', req.body);
    console.log('Request headers:', req.headers);

    const { prompt } = req.body;

    if (!prompt) {
      console.log('No prompt provided in request');
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Validate prompt is a string
    if (typeof prompt !== 'string') {
      console.log('Prompt is not a string:', typeof prompt, prompt);
      return res.status(400).json({ error: 'Prompt must be a string' });
    }

    console.log(`Starting search for: "${prompt}"`);

    // Start the search and download process
    const result = await searchAndDownload(prompt);

    res.json({
      success: true,
      message: 'Search and download completed',
      data: result
    });

  } catch (error) {
    console.error('Search error:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      error: 'Failed to process search request',
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'Server is running', timestamp: new Date().toISOString() });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📁 Downloads will be saved to: ${path.join(__dirname, '../downloads')}`);
});
