import { Observable } from '../Observable';
import { Subscriber } from '../Subscriber';
/**
 * A basic scan operation. This is used for `scan` and `reduce`.
 * @param accumulator The accumulator to use
 * @param seed The seed value for the state to accumulate
 * @param hasSeed Whether or not a seed was provided
 * @param emitOnNext Whether or not to emit the state on next
 * @param emitBeforeComplete Whether or not to emit the before completion
 */
export declare function scanInternals<V, A, S>(accumulator: (acc: V | A | S, value: V, index: number) => A, seed: S, hasSeed: boolean, emitOnNext: boolean, emitBeforeComplete?: undefined | true): (source: Observable<V>, subscriber: Subscriber<any>) => void;
//# sourceMappingURL=scanInternals.d.ts.map