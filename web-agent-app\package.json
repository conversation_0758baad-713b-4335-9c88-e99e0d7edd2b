{"name": "web-agent-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server/server.js", "start": "concurrently \"npm run server\" \"npm run dev\""}, "devDependencies": {"concurrently": "^9.2.0", "vite": "^7.0.0"}, "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "express": "^5.1.0", "puppeteer": "^24.11.2"}}