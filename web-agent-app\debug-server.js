import express from 'express';
import cors from 'cors';

console.log('Starting debug server...');

const app = express();
const PORT = 3001;

app.use(cors());
app.use(express.json());

app.get('/api/health', (req, res) => {
  console.log('Health check requested');
  res.json({ status: 'Debug server is running', timestamp: new Date().toISOString() });
});

app.post('/api/search', async (req, res) => {
  console.log('Search request received:', req.body);
  try {
    // Simple test without puppeteer first
    res.json({
      success: true,
      message: 'Debug response - no actual search performed',
      data: { test: true }
    });
  } catch (error) {
    console.error('Error:', error);
    res.status(500).json({ 
      error: 'Debug server error',
      details: error.message 
    });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Debug server running on http://localhost:${PORT}`);
});
