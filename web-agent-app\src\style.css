:root {
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --success-color: #059669;
  --error-color: #dc2626;
  --warning-color: #d97706;
  --background: #f8fafc;
  --surface: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border: #e2e8f0;
  --border-hover: #cbd5e1;

  color: var(--text-primary);
  background-color: var(--background);
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#app {
  min-height: 100vh;
  padding: 2rem;
}

.web-agent {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  color: white;
}

.header h1 {
  font-size: 3rem;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header p {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.search-section {
  background: var(--surface);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

#promptInput {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border);
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.3s ease;
}

#promptInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.search-btn:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.search-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.status-section,
.results-section,
.history-section {
  background: var(--surface);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.status-card h3,
.results-card h3,
.history-card h3 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.3rem;
}

.result-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.summary-item {
  background: var(--background);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
}

.download-list,
.failed-list {
  margin-top: 1.5rem;
}

.download-list h4 {
  color: var(--success-color);
  margin-bottom: 1rem;
}

.failed-list h4 {
  color: var(--error-color);
  margin-bottom: 1rem;
}

.download-list ul,
.failed-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.download-list li,
.failed-list li {
  background: var(--background);
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  border-left: 4px solid var(--success-color);
}

.failed-list li {
  border-left-color: var(--error-color);
}

.download-list li small,
.failed-list li small {
  color: var(--text-secondary);
  word-break: break-all;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  background: var(--background);
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid var(--primary-color);
}

.history-prompt {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.history-details {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

@media (max-width: 768px) {
  #app {
    padding: 1rem;
  }

  .header h1 {
    font-size: 2rem;
  }

  .search-section,
  .status-section,
  .results-section,
  .history-section {
    padding: 1.5rem;
  }

  .result-summary {
    grid-template-columns: 1fr;
  }

  .history-details {
    flex-direction: column;
    gap: 0.5rem;
  }
}
