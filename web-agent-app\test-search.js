// Using built-in fetch (Node.js 18+)

async function testSearch() {
  try {
    console.log('Testing search endpoint...');
    
    const response = await fetch('http://localhost:3001/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt: 'test search' }),
    });

    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      return;
    }

    const result = await response.json();
    console.log('Success:', result);
    
  } catch (error) {
    console.error('Request failed:', error.message);
  }
}

testSearch();
