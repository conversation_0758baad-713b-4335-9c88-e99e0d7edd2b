export class WebAgent {
  constructor() {
    this.isSearching = false;
    this.searchHistory = [];
  }

  render(container) {
    container.innerHTML = `
      <div class="web-agent">
        <header class="header">
          <h1>🤖 Web Agent</h1>
          <p>AI-powered search and HTML downloader</p>
        </header>

        <main class="main-content">
          <div class="search-section">
            <div class="input-group">
              <textarea 
                id="promptInput" 
                placeholder="Enter your search prompt (e.g., 'top 10 cars', 'best websites like Amazon')..."
                rows="3"
              ></textarea>
              <button id="searchBtn" class="search-btn">
                <span class="btn-text">🔍 Search & Download</span>
                <span class="btn-loading" style="display: none;">⏳ Searching...</span>
              </button>
            </div>
          </div>

          <div class="status-section" id="statusSection" style="display: none;">
            <div class="status-card">
              <h3>📊 Search Status</h3>
              <div id="statusContent"></div>
            </div>
          </div>

          <div class="results-section" id="resultsSection" style="display: none;">
            <div class="results-card">
              <h3>📁 Download Results</h3>
              <div id="resultsContent"></div>
            </div>
          </div>

          <div class="history-section" id="historySection" style="display: none;">
            <div class="history-card">
              <h3>📚 Search History</h3>
              <div id="historyContent"></div>
            </div>
          </div>
        </main>
      </div>
    `;

    this.attachEventListeners();
    this.loadSearchHistory();
  }

  attachEventListeners() {
    const searchBtn = document.getElementById('searchBtn');
    const promptInput = document.getElementById('promptInput');

    searchBtn.addEventListener('click', () => this.handleSearch());
    
    promptInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && e.ctrlKey) {
        this.handleSearch();
      }
    });
  }

  async handleSearch() {
    const promptInput = document.getElementById('promptInput');
    const prompt = promptInput.value.trim();

    if (!prompt) {
      alert('Please enter a search prompt');
      return;
    }

    if (this.isSearching) {
      return;
    }

    this.isSearching = true;
    this.updateSearchButton(true);
    this.showStatus('🚀 Starting search...');

    try {
      const response = await fetch('http://localhost:3001/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        this.showResults(result.data);
        this.addToHistory(prompt, result.data);
        this.showStatus('✅ Search completed successfully!');
      } else {
        throw new Error(result.error || 'Unknown error occurred');
      }

    } catch (error) {
      console.error('Search failed:', error);
      this.showStatus(`❌ Search failed: ${error.message}`);
    } finally {
      this.isSearching = false;
      this.updateSearchButton(false);
    }
  }

  updateSearchButton(isLoading) {
    const searchBtn = document.getElementById('searchBtn');
    const btnText = searchBtn.querySelector('.btn-text');
    const btnLoading = searchBtn.querySelector('.btn-loading');

    if (isLoading) {
      btnText.style.display = 'none';
      btnLoading.style.display = 'inline';
      searchBtn.disabled = true;
    } else {
      btnText.style.display = 'inline';
      btnLoading.style.display = 'none';
      searchBtn.disabled = false;
    }
  }

  showStatus(message) {
    const statusSection = document.getElementById('statusSection');
    const statusContent = document.getElementById('statusContent');
    
    statusContent.innerHTML = `<p>${message}</p>`;
    statusSection.style.display = 'block';
  }

  showResults(data) {
    const resultsSection = document.getElementById('resultsSection');
    const resultsContent = document.getElementById('resultsContent');

    const successResults = data.downloadResults.filter(r => r.success);
    const failedResults = data.downloadResults.filter(r => !r.success);

    resultsContent.innerHTML = `
      <div class="result-summary">
        <div class="summary-item">
          <strong>📁 Folder:</strong> ${data.folderName}
        </div>
        <div class="summary-item">
          <strong>📊 Total Results:</strong> ${data.totalResults}
        </div>
        <div class="summary-item">
          <strong>✅ Successful:</strong> ${data.successCount}
        </div>
        <div class="summary-item">
          <strong>❌ Failed:</strong> ${data.failCount}
        </div>
      </div>

      ${successResults.length > 0 ? `
        <div class="download-list">
          <h4>✅ Successfully Downloaded:</h4>
          <ul>
            ${successResults.map(result => `
              <li>
                <strong>${result.filename}</strong>
                <br><small>${result.url}</small>
              </li>
            `).join('')}
          </ul>
        </div>
      ` : ''}

      ${failedResults.length > 0 ? `
        <div class="failed-list">
          <h4>❌ Failed Downloads:</h4>
          <ul>
            ${failedResults.map(result => `
              <li>
                <strong>${result.url}</strong>
                <br><small>Error: ${result.error}</small>
              </li>
            `).join('')}
          </ul>
        </div>
      ` : ''}
    `;

    resultsSection.style.display = 'block';
  }

  addToHistory(prompt, result) {
    const historyItem = {
      prompt,
      timestamp: new Date().toISOString(),
      folderName: result.folderName,
      successCount: result.successCount,
      totalResults: result.totalResults
    };

    this.searchHistory.unshift(historyItem);
    
    // Keep only last 10 searches
    if (this.searchHistory.length > 10) {
      this.searchHistory = this.searchHistory.slice(0, 10);
    }

    this.saveSearchHistory();
    this.displayHistory();
  }

  loadSearchHistory() {
    try {
      const saved = localStorage.getItem('webAgentHistory');
      if (saved) {
        this.searchHistory = JSON.parse(saved);
        this.displayHistory();
      }
    } catch (error) {
      console.error('Failed to load search history:', error);
    }
  }

  saveSearchHistory() {
    try {
      localStorage.setItem('webAgentHistory', JSON.stringify(this.searchHistory));
    } catch (error) {
      console.error('Failed to save search history:', error);
    }
  }

  displayHistory() {
    if (this.searchHistory.length === 0) return;

    const historySection = document.getElementById('historySection');
    const historyContent = document.getElementById('historyContent');

    historyContent.innerHTML = `
      <div class="history-list">
        ${this.searchHistory.map(item => `
          <div class="history-item">
            <div class="history-prompt">"${item.prompt}"</div>
            <div class="history-details">
              <span>📁 ${item.folderName}</span>
              <span>✅ ${item.successCount}/${item.totalResults}</span>
              <span>🕒 ${new Date(item.timestamp).toLocaleString()}</span>
            </div>
          </div>
        `).join('')}
      </div>
    `;

    historySection.style.display = 'block';
  }
}
